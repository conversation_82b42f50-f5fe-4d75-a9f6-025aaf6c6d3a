/**
 * Script para lidar com OAuth em WebView Android
 * Fornece interfaces para comunicação com o app nativo
 */

(function() {
  'use strict';

  // Detectar se está em WebView Android
  function isAndroidWebView() {
    const ua = navigator.userAgent.toLowerCase();
    return /android/i.test(ua) && 
           (/wv/.test(ua) || /webview/.test(ua) || 
            'Android' in window || 'AndroidInterface' in window);
  }

  if (!isAndroidWebView()) {
    return;
  }

  console.log('🔐 WebView OAuth Handler carregado');

  // Interface global para OAuth
  window.WebViewOAuthHandler = {
    // Abrir URL OAuth em Custom Tabs
    openOAuthUrl: function(url) {
      console.log('🌐 Tentando abrir OAuth URL:', url);
      
      try {
        // Método 1: Interface Android personalizada
        if (window.Android && window.Android.openCustomTab) {
          window.Android.openCustomTab(url);
          return { success: true, method: 'Android.openCustomTab' };
        }
        
        // Método 2: AndroidInterface
        if (window.AndroidInterface && window.AndroidInterface.openCustomTab) {
          window.AndroidInterface.openCustomTab(url);
          return { success: true, method: 'AndroidInterface.openCustomTab' };
        }
        
        // Método 3: Interface genérica para Custom Tabs
        if (window.Android && window.Android.openUrl) {
          window.Android.openUrl(url);
          return { success: true, method: 'Android.openUrl' };
        }
        
        // Método 4: Tentar window.open com _system
        window.open(url, '_system');
        return { success: true, method: 'window.open(_system)' };
        
      } catch (error) {
        console.error('❌ Erro ao abrir OAuth URL:', error);
        return { success: false, error: error.message };
      }
    },

    // Abrir página atual no navegador externo
    openInExternalBrowser: function() {
      const currentUrl = window.location.href;
      console.log('🌐 Abrindo no navegador externo:', currentUrl);
      
      try {
        // Método 1: Interface específica para navegador externo
        if (window.Android && window.Android.openExternalBrowser) {
          window.Android.openExternalBrowser(currentUrl);
          return { success: true, method: 'Android.openExternalBrowser' };
        }
        
        // Método 2: AndroidInterface
        if (window.AndroidInterface && window.AndroidInterface.openExternalBrowser) {
          window.AndroidInterface.openExternalBrowser(currentUrl);
          return { success: true, method: 'AndroidInterface.openExternalBrowser' };
        }
        
        // Método 3: Fallback genérico
        if (window.Android && window.Android.openUrl) {
          window.Android.openUrl(currentUrl);
          return { success: true, method: 'Android.openUrl' };
        }
        
        // Método 4: window.open
        window.open(currentUrl, '_system');
        return { success: true, method: 'window.open(_system)' };
        
      } catch (error) {
        console.error('❌ Erro ao abrir navegador externo:', error);
        return { success: false, error: error.message };
      }
    },

    // Verificar se Custom Tabs está disponível
    isCustomTabsAvailable: function() {
      return !!(
        (window.Android && window.Android.openCustomTab) ||
        (window.AndroidInterface && window.AndroidInterface.openCustomTab)
      );
    },

    // Verificar se navegador externo está disponível
    isExternalBrowserAvailable: function() {
      return !!(
        (window.Android && window.Android.openExternalBrowser) ||
        (window.AndroidInterface && window.AndroidInterface.openExternalBrowser) ||
        (window.Android && window.Android.openUrl)
      );
    },

    // Obter informações do ambiente
    getEnvironmentInfo: function() {
      return {
        userAgent: navigator.userAgent,
        isWebView: isAndroidWebView(),
        hasAndroidInterface: 'Android' in window,
        hasAndroidInterfaceAlt: 'AndroidInterface' in window,
        customTabsAvailable: this.isCustomTabsAvailable(),
        externalBrowserAvailable: this.isExternalBrowserAvailable(),
        availableMethods: this.getAvailableMethods()
      };
    },

    // Listar métodos disponíveis
    getAvailableMethods: function() {
      const methods = [];
      
      if (window.Android) {
        if (window.Android.openCustomTab) methods.push('Android.openCustomTab');
        if (window.Android.openExternalBrowser) methods.push('Android.openExternalBrowser');
        if (window.Android.openUrl) methods.push('Android.openUrl');
      }
      
      if (window.AndroidInterface) {
        if (window.AndroidInterface.openCustomTab) methods.push('AndroidInterface.openCustomTab');
        if (window.AndroidInterface.openExternalBrowser) methods.push('AndroidInterface.openExternalBrowser');
      }
      
      methods.push('window.open(_system)');
      
      return methods;
    }
  };

  // Expor função global para facilitar o uso
  window.openOAuthInCustomTabs = function(url) {
    return window.WebViewOAuthHandler.openOAuthUrl(url);
  };

  window.openInExternalBrowser = function() {
    return window.WebViewOAuthHandler.openInExternalBrowser();
  };

  // Evento para notificar que o handler está pronto
  const readyEvent = new CustomEvent('webview-oauth-ready', {
    detail: window.WebViewOAuthHandler.getEnvironmentInfo()
  });
  document.dispatchEvent(readyEvent);

  console.log('✅ WebView OAuth Handler pronto');
  console.log('📱 Métodos disponíveis:', window.WebViewOAuthHandler.getAvailableMethods());

})();
