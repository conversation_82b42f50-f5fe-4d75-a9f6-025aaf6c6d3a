import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, RefreshCw, ExternalLink, AlertCircle } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useNotification } from "@/context/NotificationContext";
import { useWebViewVisibility } from "@/hooks/useWebViewVisibility";

interface WebViewLoginStatusProps {
  onLoginSuccess: () => void;
  onRetry: () => void;
}

export const WebViewLoginStatus = ({ onLoginSuccess, onRetry }: WebViewLoginStatusProps) => {
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);
  const [autoCheckCount, setAutoCheckCount] = useState(0);
  const { user, refreshAuth } = useAuth();
  const { showNotification } = useNotification();

  // Hook para detectar quando o usuário volta do navegador
  const { markAsHidden } = useWebViewVisibility({
    onReturnFromBackground: () => {
      console.log('🔄 Usuário voltou do navegador - verificando login automaticamente');
      handleCheckLogin();
    }
  });

  // Verificar automaticamente quando o componente é montado
  useEffect(() => {
    const checkTimer = setTimeout(() => {
      handleCheckLogin();
    }, 2000);

    return () => clearTimeout(checkTimer);
  }, []);

  // Marcar como oculto quando o componente é montado (usuário acabou de sair para o navegador)
  useEffect(() => {
    markAsHidden();
  }, [markAsHidden]);

  // Verificar se o usuário fez login
  const handleCheckLogin = async () => {
    try {
      setIsChecking(true);
      setLastCheck(new Date());
      setAutoCheckCount(prev => prev + 1);

      console.log(`🔍 Verificando login (tentativa ${autoCheckCount + 1})`);

      // Forçar refresh da autenticação
      await refreshAuth();

      // Aguardar um pouco para o estado se propagar
      setTimeout(() => {
        if (user) {
          console.log('✅ Login detectado com sucesso!');
          showNotification({
            title: "Login realizado com sucesso!",
            description: "Bem-vindo de volta! Você está autenticado.",
            type: "success",
            buttonText: "Continuar"
          });
          onLoginSuccess();
        } else {
          console.log('❌ Login ainda não detectado');

          // Se é uma verificação automática e ainda não encontrou login
          if (autoCheckCount < 3) {
            showNotification({
              title: "Verificando login...",
              description: "Ainda não detectamos o login. Certifique-se de ter completado o processo no navegador.",
              type: "info",
              buttonText: "Ok",
              duration: 3000
            });
          }
        }
      }, 1000);

    } catch (error) {
      console.error('Erro ao verificar login:', error);
      showNotification({
        title: "Erro ao verificar",
        description: "Não foi possível verificar o status do login. Tente novamente.",
        type: "error",
        buttonText: "Ok"
      });
    } finally {
      setIsChecking(false);
    }
  };

  // Abrir página de login no navegador novamente
  const handleOpenLoginPage = () => {
    try {
      const loginUrl = `${window.location.origin}/`;
      
      // Tentar diferentes métodos para abrir
      const opened = window.open(loginUrl, '_blank') || 
                    window.open(loginUrl, '_system') || 
                    window.open(loginUrl, '_external');
      
      if (!opened) {
        // Fallback: criar link e simular clique
        const link = document.createElement('a');
        link.href = loginUrl;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      showNotification({
        title: "Abrindo página de login...",
        description: "Complete o login no navegador e volte aqui para verificar.",
        type: "info",
        buttonText: "Ok"
      });

    } catch (error) {
      console.error('Erro ao abrir página de login:', error);
      showNotification({
        title: "Erro",
        description: "Não foi possível abrir o navegador. Tente atualizar a página.",
        type: "error",
        buttonText: "Ok"
      });
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto border-blue-200 bg-blue-50/50">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
          <ExternalLink className="w-6 h-6 text-blue-600" />
        </div>
        <CardTitle className="text-lg text-blue-800">
          Login no Navegador Externo
        </CardTitle>
        <CardDescription className="text-blue-700">
          O login foi aberto no navegador. Após completar o login, volte aqui e clique em "Verificar Login".
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status do último check */}
        {lastCheck && (
          <div className="text-xs text-gray-600 text-center p-2 bg-gray-50 rounded">
            Última verificação: {lastCheck.toLocaleTimeString()}
          </div>
        )}

        {/* Botão principal - Verificar Login */}
        <Button
          onClick={handleCheckLogin}
          disabled={isChecking}
          className="w-full bg-green-600 hover:bg-green-700 text-white"
          size="lg"
        >
          {isChecking ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Verificando...
            </>
          ) : (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Verificar Login
            </>
          )}
        </Button>
        
        <div className="text-xs text-gray-600 text-center px-2">
          Clique após completar o login no navegador
        </div>
        
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-blue-50 px-2 text-gray-500">ou</span>
          </div>
        </div>
        
        {/* Botão secundário - Abrir login novamente */}
        <Button
          onClick={handleOpenLoginPage}
          variant="outline"
          className="w-full border-blue-300 hover:bg-blue-50"
          size="lg"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Abrir Login Novamente
        </Button>
        
        <div className="text-xs text-gray-600 text-center px-2">
          Se o navegador não abriu ou você fechou por engano
        </div>
        
        {/* Botão terciário - Tentar método diferente */}
        <div className="pt-2 border-t border-gray-200">
          <Button
            onClick={onRetry}
            variant="ghost"
            className="w-full text-gray-600 hover:text-gray-800"
            size="sm"
          >
            <AlertCircle className="w-4 h-4 mr-2" />
            Tentar Método Diferente
          </Button>
        </div>

        {/* Instruções adicionais */}
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="text-xs text-yellow-800">
            <strong>💡 Dica:</strong> Se o login não funcionar, tente:
            <ul className="mt-1 ml-4 list-disc">
              <li>Atualizar esta página</li>
              <li>Limpar cache do navegador</li>
              <li>Usar login por email e senha</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WebViewLoginStatus;
