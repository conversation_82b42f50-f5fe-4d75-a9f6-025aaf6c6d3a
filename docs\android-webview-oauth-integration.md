# Integração OAuth para WebView Android

Este documento descreve como implementar as interfaces necessárias no aplicativo Android para resolver o problema de login com Google em WebView.

## 🔍 Problema

O Google bloqueia OAuth em WebViews por questões de segurança. Quando o usuário tenta fazer login com Google dentro do WebView, o Google detecta que não é um navegador "real" e bloqueia a autenticação.

## 💡 Solução

Implementar interfaces JavaScript no WebView que permitam abrir o OAuth em Custom Tabs (Chrome Custom Tabs) ou no navegador externo.

## 🛠️ Implementação Android

### 1. Interface JavaScript Principal

Adicione esta interface ao seu WebView:

```kotlin
class WebViewOAuthInterface(private val context: Context, private val activity: Activity) {
    
    @JavascriptInterface
    fun openCustomTab(url: String) {
        activity.runOnUiThread {
            try {
                val builder = CustomTabsIntent.Builder()
                val customTabsIntent = builder.build()
                customTabsIntent.launchUrl(context, Uri.parse(url))
            } catch (e: Exception) {
                // Fallback para navegador externo
                openExternalBrowser(url)
            }
        }
    }
    
    @JavascriptInterface
    fun openExternalBrowser(url: String? = null) {
        activity.runOnUiThread {
            try {
                val urlToOpen = url ?: "https://pedb.com.br"
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(urlToOpen))
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            } catch (e: Exception) {
                Log.e("WebViewOAuth", "Erro ao abrir navegador externo", e)
            }
        }
    }
    
    @JavascriptInterface
    fun openUrl(url: String) {
        // Método genérico - tenta Custom Tabs primeiro
        openCustomTab(url)
    }
}
```

### 2. Configuração do WebView

Configure o WebView para usar a interface:

```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var webView: WebView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        webView = findViewById(R.id.webview)
        
        // Configurações do WebView
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            setSupportMultipleWindows(true)
            javaScriptCanOpenWindowsAutomatically = true
        }
        
        // Adicionar interfaces JavaScript
        webView.addJavascriptInterface(
            WebViewOAuthInterface(this, this), 
            "Android"
        )
        
        // Interface alternativa (para compatibilidade)
        webView.addJavascriptInterface(
            WebViewOAuthInterface(this, this), 
            "AndroidInterface"
        )
        
        // Configurar WebViewClient para interceptar URLs
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                val url = request?.url?.toString()
                
                // Interceptar URLs de callback OAuth
                if (url?.contains("auth/callback") == true) {
                    // Deixar o WebView processar o callback
                    return false
                }
                
                return super.shouldOverrideUrlLoading(view, request)
            }
        }
        
        webView.loadUrl("https://pedb.com.br")
    }
}
```

### 3. Dependências Necessárias

Adicione ao `build.gradle` (Module: app):

```gradle
dependencies {
    implementation 'androidx.browser:browser:1.7.0'
    // ... outras dependências
}
```

### 4. Permissões no Manifest

Adicione ao `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.INTERNET" />

<!-- Para Custom Tabs -->
<queries>
    <intent>
        <action android:name="android.support.customtabs.action.CustomTabsService" />
    </intent>
</queries>
```

### 5. Deep Link Configuration (Opcional)

Para capturar o retorno do OAuth, configure deep links:

```xml
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTop">
    
    <!-- Intent filter para deep links -->
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https"
              android:host="pedb.com.br"
              android:pathPrefix="/auth/callback" />
    </intent-filter>
</activity>
```

## 🧪 Teste da Implementação

### 1. Verificar Interfaces Disponíveis

O JavaScript pode verificar quais métodos estão disponíveis:

```javascript
// Verificar se as interfaces estão disponíveis
console.log('Android interface:', 'Android' in window);
console.log('AndroidInterface:', 'AndroidInterface' in window);

// Verificar métodos específicos
if (window.Android) {
    console.log('openCustomTab:', typeof window.Android.openCustomTab);
    console.log('openExternalBrowser:', typeof window.Android.openExternalBrowser);
}
```

### 2. Testar OAuth

1. Abra o app Android
2. Navegue para a página de login
3. Clique em "Entrar com Google"
4. Verifique se abre Custom Tabs ou navegador externo
5. Complete o login
6. Verifique se retorna ao app autenticado

## 🔧 Troubleshooting

### Problema: Custom Tabs não abre
- Verifique se o Chrome está instalado
- Teste com `openExternalBrowser` como fallback

### Problema: Interface JavaScript não funciona
- Verifique se `javaScriptEnabled = true`
- Confirme que a interface foi adicionada corretamente
- Teste com `console.log` no JavaScript

### Problema: OAuth não retorna ao app
- Configure deep links corretamente
- Verifique as URLs de callback no Supabase

## 📱 Fluxo Completo

1. **Usuário clica "Login com Google"**
2. **JavaScript detecta WebView**
3. **Chama `window.Android.openCustomTab(oauthUrl)`**
4. **Android abre Custom Tabs com URL OAuth**
5. **Usuário faz login no Google**
6. **Google redireciona para callback URL**
7. **Deep link retorna ao app (opcional)**
8. **WebView processa o callback e autentica**

## 🎯 Benefícios

- ✅ Resolve bloqueio do Google OAuth
- ✅ Mantém experiência nativa
- ✅ Fallback automático para navegador
- ✅ Compatível com diferentes versões Android
- ✅ Não requer mudanças no backend

## 📞 Suporte

Se houver problemas na implementação, verifique:

1. Logs do Android Studio
2. Console do WebView (chrome://inspect)
3. Network tab para requisições OAuth
4. Configurações do Supabase OAuth
