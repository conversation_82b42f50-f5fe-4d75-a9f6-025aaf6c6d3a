import { useEffect, useRef } from 'react';

interface UseWebViewVisibilityProps {
  onVisibilityChange?: (isVisible: boolean) => void;
  onReturnFromBackground?: () => void;
}

/**
 * Hook para detectar quando o usuário volta do navegador externo em WebView
 * Útil para verificar se o login OAuth foi completado
 */
export const useWebViewVisibility = ({ 
  onVisibilityChange, 
  onReturnFromBackground 
}: UseWebViewVisibilityProps = {}) => {
  const wasHiddenRef = useRef(false);
  const lastVisibilityChangeRef = useRef<number>(0);

  useEffect(() => {
    // Detectar se está em WebView
    const isWebView = () => {
      const ua = navigator.userAgent.toLowerCase();
      const isAndroid = /android/i.test(ua);
      const isWebView = /wv/.test(ua) || /webview/.test(ua);
      const hasAndroidInterface = 'Android' in window || 'AndroidInterface' in window;
      const isChrome = /chrome/.test(ua) && !/edg/.test(ua);
      const isSamsung = /samsungbrowser/.test(ua);
      
      return isAndroid && (isWebView || hasAndroidInterface || (!isChrome && !isSamsung));
    };

    if (!isWebView()) {
      return; // Não é WebView, não fazer nada
    }

    console.log('👁️ Configurando monitoramento de visibilidade para WebView');

    // Handler para mudanças de visibilidade
    const handleVisibilityChange = () => {
      const now = Date.now();
      const isHidden = document.hidden;
      
      // Evitar múltiplos eventos muito próximos
      if (now - lastVisibilityChangeRef.current < 500) {
        return;
      }
      
      lastVisibilityChangeRef.current = now;

      console.log(`👁️ Visibilidade mudou: ${isHidden ? 'oculto' : 'visível'}`);

      if (onVisibilityChange) {
        onVisibilityChange(!isHidden);
      }

      // Se estava oculto e agora está visível, usuário voltou
      if (wasHiddenRef.current && !isHidden) {
        console.log('🔄 Usuário voltou do background - possível retorno do navegador');
        
        if (onReturnFromBackground) {
          // Aguardar um pouco para garantir que a página está totalmente carregada
          setTimeout(() => {
            onReturnFromBackground();
          }, 1000);
        }
      }

      wasHiddenRef.current = isHidden;
    };

    // Handler para foco da janela
    const handleFocus = () => {
      console.log('🎯 Janela recebeu foco');
      
      if (wasHiddenRef.current && onReturnFromBackground) {
        console.log('🔄 Retorno detectado via focus event');
        setTimeout(() => {
          onReturnFromBackground();
        }, 500);
      }
    };

    // Handler para blur da janela
    const handleBlur = () => {
      console.log('😴 Janela perdeu foco');
      wasHiddenRef.current = true;
    };

    // Handler para mudanças no estado da página
    const handlePageShow = (event: PageTransitionEvent) => {
      console.log('📄 Page show event:', { persisted: event.persisted });
      
      if (event.persisted && onReturnFromBackground) {
        console.log('🔄 Retorno detectado via pageshow event');
        setTimeout(() => {
          onReturnFromBackground();
        }, 500);
      }
    };

    const handlePageHide = () => {
      console.log('📄 Page hide event');
      wasHiddenRef.current = true;
    };

    // Adicionar event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);
    window.addEventListener('pageshow', handlePageShow);
    window.addEventListener('pagehide', handlePageHide);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      window.removeEventListener('pageshow', handlePageShow);
      window.removeEventListener('pagehide', handlePageHide);
      
      console.log('🧹 Monitoramento de visibilidade removido');
    };
  }, [onVisibilityChange, onReturnFromBackground]);

  return {
    // Função para marcar manualmente que o usuário saiu (ex: ao abrir navegador)
    markAsHidden: () => {
      wasHiddenRef.current = true;
      console.log('👁️ Marcado manualmente como oculto');
    },
    
    // Função para verificar se estava oculto
    wasHidden: () => wasHiddenRef.current
  };
};

export default useWebViewVisibility;
