import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, ExternalLink, Mail, Smartphone } from "lucide-react";
import { useNotification } from "@/context/NotificationContext";

interface WebViewAuthHelperProps {
  onEmailLogin: () => void;
  onRetryGoogle: () => void;
}

export const WebViewAuthHelper = ({ onEmailLogin, onRetryGoogle }: WebViewAuthHelperProps) => {
  const { showNotification } = useNotification();
  const [isOpeningBrowser, setIsOpeningBrowser] = useState(false);

  const handleOpenInBrowser = async () => {
    try {
      setIsOpeningBrowser(true);

      // Usar o handler OAuth se disponível
      if ((window as any).WebViewOAuthHandler) {
        const result = (window as any).WebViewOAuthHandler.openInExternalBrowser();
        console.log('🌐 Resultado do navegador externo:', result);

        if (result.success) {
          showNotification({
            title: "Abrindo navegador...",
            description: "Complete o login no navegador e retorne ao app.",
            type: "info",
            buttonText: "Ok"
          });
          return;
        }
      }

      // Fallback para métodos antigos
      const currentUrl = window.location.href;

      if ((window as any).Android?.openExternalBrowser) {
        (window as any).Android.openExternalBrowser(currentUrl);
      } else if ((window as any).AndroidInterface?.openExternalBrowser) {
        (window as any).AndroidInterface.openExternalBrowser(currentUrl);
      } else {
        window.open(currentUrl, '_system');
      }

      showNotification({
        title: "Abrindo navegador...",
        description: "Complete o login no navegador e retorne ao app.",
        type: "info",
        buttonText: "Ok"
      });

    } catch (error) {
      console.error('Erro ao abrir navegador externo:', error);
      showNotification({
        title: "Erro",
        description: "Não foi possível abrir o navegador. Use o login por email.",
        type: "error",
        buttonText: "Ok"
      });
    } finally {
      setIsOpeningBrowser(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto border-orange-200 bg-orange-50/50">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
          <AlertCircle className="w-6 h-6 text-orange-600" />
        </div>
        <CardTitle className="text-lg text-orange-800">
          Login com Google Bloqueado
        </CardTitle>
        <CardDescription className="text-orange-700">
          O Google bloqueia login em aplicativos por segurança. Escolha uma das opções abaixo:
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Opção 1: Abrir no navegador */}
        <Button
          onClick={handleOpenInBrowser}
          disabled={isOpeningBrowser}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          size="lg"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          {isOpeningBrowser ? "Abrindo..." : "Abrir no Navegador"}
        </Button>
        
        <div className="text-xs text-gray-600 text-center px-2">
          Abre esta página no navegador onde o login com Google funciona normalmente
        </div>
        
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-orange-50 px-2 text-gray-500">ou</span>
          </div>
        </div>
        
        {/* Opção 2: Login por email */}
        <Button
          onClick={onEmailLogin}
          variant="outline"
          className="w-full border-gray-300 hover:bg-gray-50"
          size="lg"
        >
          <Mail className="w-4 h-4 mr-2" />
          Fazer Login com Email
        </Button>
        
        <div className="text-xs text-gray-600 text-center px-2">
          Use seu email e senha para acessar sua conta
        </div>
        
        {/* Opção 3: Tentar novamente */}
        <div className="pt-2 border-t border-gray-200">
          <Button
            onClick={onRetryGoogle}
            variant="ghost"
            className="w-full text-gray-600 hover:text-gray-800"
            size="sm"
          >
            <Smartphone className="w-4 h-4 mr-2" />
            Tentar Google Novamente
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default WebViewAuthHelper;
